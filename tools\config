#path to svn with resources.
#you MUST set this option
resources = '../gambino-resources/mobile-web/en/low/'
#website = False

#path to ViewGenerator tool
#view_generator_path = 'syd_view_generator/view_generator'

#resource locale used
locales = ['en']

#resource resolutions
resolutions = ['low']

#path to dart binary (or put it into your $PATH)
#dart_binary = 'dart'

#path to pub binary (or put it into your $PATH)
#pub_binary = 'pub.bat'

#path to dartfmt binary (or put it into your $PATH)
#dartfmt_binary = 'dartfmt.bat'

#path to svn binary (or put it into your $PATH)
#svn_binary = 'svn'

#path to git util binary
#gsutil_binary = 'gsutil

#allowed_assets = ['lobby', 'games', 'debug']

#path to git binary (or put it into your $PATH)
#git_binary = 'git'

#specific resource revision
#see -r option in 'svn help up' for possible values
#lobby_revision = '32258'

#games revision
#games_revision = '29899'
