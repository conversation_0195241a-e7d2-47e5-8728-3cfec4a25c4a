#!/usr/bin/env python

import subprocess
import argparse
import os
import sys
import glob
import shutil
import fileinput
from config import config
import json
import distutils.dir_util
import re
import requests
from random import randrange
import pathlib

CONFIG_FILE_NAME = 'config'
PROJ_FILES = os.path.abspath('../src')
BASE_VIEW_NAMESPACE = 'view_base'
LOBBY_FOLDER_SRC = os.path.join('lobby_next_version')
#LOBBY_STANDALONE =  os.path.join('Lobby_standalone_web') #do not use for a while (required by one slot mode)
LOBBY_FOLDER_SRC_SECONDARY = 'Lobby_feature_3'
LOBBY_FOLDER_SRC_ADDITIONAL = ''
FEATURES_FOLDER_SRC = 'Features'
FEATURES_FOLDER_DEST = 'Features'
DEBUG_FOLDER_SRC = 'debug'
GAMES_FOLDER_SRC = 'games'
GAMES_COMMON_FOLDER_SRC = 'games/common_next_version'
MAP_FILE_EXT = "map.json"
DST_DART_MODULE = 'gambino'
LOBBY_FOLDERS = [LOBBY_FOLDER_SRC, DEBUG_FOLDER_SRC]
GEN_VIEWS_FOLDERS = [LOBBY_FOLDER_SRC, GAMES_COMMON_FOLDER_SRC, DEBUG_FOLDER_SRC]
GAMES_FOLDERS = [GAMES_FOLDER_SRC]

class ExternalResourceBundle:
    def __init__(self, bundle_name, src_folder, view_gen_path, namespace, class_name_prefix = "", svn_version ='HEAD', src_files = None, patch_folder = None, patch_version = 'HEAD', use_root_in_view_generator = False):
        self.bundle_name = bundle_name
        self.src_folder = src_folder
        self.view_gen_path =  view_gen_path
        self.namespace = namespace
        self.class_name_prefix = class_name_prefix
        self.svn_version = svn_version
        self.src_files = src_files
        self.patch_folder = patch_folder
        self.patch_version = patch_version
        self.use_root_in_view_generator = use_root_in_view_generator


RESOURCE_BUNDLES = [
    ExternalResourceBundle(
        'Competition',
        'Features/CollaborationChallenges/',
        'features/lib/src/competitions/base_views/',
        'features',
        svn_version='237195'),
    ExternalResourceBundle(
        'Halloween', 
        'Features/Halloween/',
        'feature_plugins/lib/src/halloween/base_views/',
        'feature_plugins',
        svn_version='315395'),
    ExternalResourceBundle(
        'AlbumHLV7',
        'Features/AlbumHLV7/',
        'feature_plugins/lib/src/album_v6/base_views/',
        'feature_plugins',
        svn_version='295208'),
    ExternalResourceBundle(
        'WheelOfWins', 
        'Features/WheelOfWins/',
        'feature_plugins/lib/src/wheel_of_wins/base_views/',
        'feature_plugins',
        svn_version='237195'),
    ExternalResourceBundle(
        'WheelOfWinsHRR', 
        'Features/WheelOfWinsHRR/',
        'feature_plugins/lib/src/wheel_of_wins_hrr/base_views/',
        'feature_plugins',
        svn_version='295208'),
    ExternalResourceBundle(
        'SnakesAndLadders', 
        'Features/SnakesAndLadders/', 
        'feature_plugins/lib/src/snakes_and_ladders/base_views/',
        'feature_plugins',
        svn_version='295208',
        use_root_in_view_generator=True),
    ExternalResourceBundle(
        'PlinGo', 
        'Features/PlinGo/',
        'feature_plugins/lib/src/plinko/base_views/',
        'feature_plugins',
        svn_version='295208'),
    ExternalResourceBundle(
        'TurboWins', 
        'Features/TurboWins/', 
        'feature_plugins/lib/src/turbo_wins/base_views',
        'feature_plugins',
        svn_version='340014'),
    ExternalResourceBundle(
        'FarmHL', 
        'Features/FarmHL/', 
        'feature_plugins/lib/src/farm_hl/base_views',
        'feature_plugins',
        svn_version='295208'),
    ExternalResourceBundle(
        'PiratesV2', 
        'Features/PiratesV2/', 
        'feature_plugins/lib/src/pirates/base_views',
        'feature_plugins',
        svn_version='295208'),
    ExternalResourceBundle(
        'Challenge', 
        'Features/ChallengeOld/', 
        'features/lib/src/challenges/base_views',
        'features',
        svn_version='336543'),
    ExternalResourceBundle(
        'War', 
        'Features/War/', 
        'feature_plugins/lib/src/war/base_views',
        'feature_plugins',
        svn_version='295208'),
    ExternalResourceBundle(
        'BoardGame', 
        'Features/BoardGame/', 
        'feature_plugins/lib/src/gold_adventure_game/base_views',
        'feature_plugins',
        svn_version='292453'),
    ExternalResourceBundle(
        'Words',
        'Features/Words/',
        'feature_plugins/lib/src/words/base_views',
        'feature_plugins',
        svn_version='295208'),
    ExternalResourceBundle(
        'CashInVegas',
        'Features/CashInVegas/',
        'feature_plugins/lib/src/cash_in_vegas/base_views',
        'feature_plugins',
        svn_version='295208'),
    ExternalResourceBundle(
        'GabyTutorial',
        'Features/GabyTutorial/',
        'feature_plugins/lib/src/gaby_tutorial/base_views',
        'feature_plugins',
        svn_version='295208'),
    ExternalResourceBundle(
        'FantasticRace',
        'Features/FantasticRace/',
        'feature_plugins/lib/src/fantastic_race/base_views',
        'feature_plugins',
        svn_version='295208'),
    ExternalResourceBundle(
        'LevelMap', 
        'Features/LevelUp/', 
        'feature_plugins/lib/src/level_map/base_views',
        'feature_plugins',
        svn_version='288676'),
    ExternalResourceBundle(
        'Gambingo', 
        'Features/Gambingo/', 
        'feature_plugins/lib/src/gambingo/base_views',
        'feature_plugins',
        svn_version='295208'),
    ExternalResourceBundle(
        'BuySlotFeature', 
        'Features/BuySlotFeature/', 
        'feature_plugins/lib/src/buy_slot_feature/base_views',
        'feature_plugins',
        svn_version='237195'),
    ExternalResourceBundle(
        'GiftsCalendar', 
        'Features/DailyBonusCalendar/', 
        'feature_plugins/lib/src/gifts_calendar/base_views',
        'feature_plugins',
        svn_version='237195'),
    ExternalResourceBundle(
        'RubyRush', 
        'Features/RubyRush/', 
        'feature_plugins/lib/src/ruby_rush/base_views',
        'feature_plugins',
        svn_version='295208'),
    ExternalResourceBundle(
        'PuzzleHl', 
        'Features/PuzzleHl/', 
        'feature_plugins/lib/src/puzzle_hl/base_views',
        'feature_plugins',
        svn_version='295208'),
    ExternalResourceBundle(
        'CloverChase', 
        'Features/CloverChase/', 
        'feature_plugins/lib/src/clover_chase/base_views',
        'feature_plugins',
        svn_version='297005'),
    ExternalResourceBundle(
        'HotReels', 
        'Features/HotReels/', 
        'feature_plugins/lib/src/hot_reels/baseviews',
        'feature_plugins',
        svn_version='241486'),
    ExternalResourceBundle(
        'GalacticAdventureHl', 
        'Features/GalacticAdventureHl/', 
        'feature_plugins/lib/src/galactic/base_views',
        'feature_plugins',
        svn_version='295208'),
    ExternalResourceBundle(
        'SideBet', 
        'Features/SideBet/', 
        'feature_plugins/lib/src/side_bet/base_views',
        'feature_plugins',
        svn_version='261820'),
    ExternalResourceBundle(
        'XmasMagic', 
        'Features/ChristmasMagicHL/', 
        'feature_plugins/lib/src/xmas_magic/base_views',
        'feature_plugins',
        svn_version='272394'),
     ExternalResourceBundle(
        'CandylandMagic', 
        'Features/CandylandMagicHL/', 
        'feature_plugins/lib/src/candyland_magic/base_views',
        'feature_plugins',
        svn_version='296058'),
    ExternalResourceBundle(
        'EndlessOffer', 
        'Features/EndlessOffer/', 
        'feature_plugins/lib/src/endless_offer/base_views',
        'feature_plugins',
        svn_version='294467'),
    ExternalResourceBundle(
        'FreeSpinsLevelUp', 
        'games/game177/feature/LevelUpFS/', 
        'feature_plugins/lib/src/free_spins_level_up/base_views',
        'feature_plugins',
        svn_version='305047'),
    ExternalResourceBundle(
        'ScratchCards', 
        'Features/ScratchCard/',
        'feature_plugins/lib/src/scratch_cards/base_views',
        'feature_plugins',
        svn_version='301885'),
    ExternalResourceBundle(
        'Tournaments', 
        'Features/TournamentsOld/',
        'feature_plugins/lib/src/tournaments/base_views',
        'feature_plugins',
        svn_version='328137'),
    ExternalResourceBundle(
        'FishingHL', 
        'Features/FishingHL/',
        'feature_plugins/lib/src/fishing/base_views',
        'feature_plugins',
        svn_version='339253'),
    ExternalResourceBundle(
        'SpinRoyale', 
        'Features/SpinRoyale/',
        'feature_plugins/lib/src/spin_royale/base_views',
        'feature_plugins',
        svn_version='340496')
]

TO_REMOVE = [
    'JackpotInLobby.wad.xml',
    'JackpotWin.wad.xml',
    'HRRchoose.wad.xml',
    'popups/JackpotInLobby',
    'popups/JackpotWin',
    'popups/HRRchoose',
    'GambingoPopups.wad.xml',
    'GambingoItems.wad.xml',
    'GambingoInLobby.wad.xml',
    'Gambingo_panel.wad.xml',
    'Gambingo.wad.xml',
    'popups/GambingoPopups',
    'popups/GambingoItems',
    'popups/GambingoInLobby',
    'popups/Gambingo',
    'Gambingo_panel',
]

def delete_link(linkName):
    if os.name == 'nt':
        if os.path.exists(linkName):
            cmd = [
                'cmd.exe',
                '/c',
                'rmdir',
                '/q',
                linkName
            ]
            print "Executing", ' '.join(cmd)
            try:
                subprocess.check_call(cmd)
            except subprocess.CalledProcessError as e:
                cmd.insert(len(cmd) - 2, '/s')
                print "Try harder with rmdir:", ' '.join(cmd)
                subprocess.check_call(cmd)
    else:
        if os.path.islink(linkName):
            os.unlink(linkName)
        elif os.path.isdir(linkName):
            shutil.rmtree(linkName)
        elif os.path.exists(linkName):
            os.unlink(linkName)

def make_links(linkName, target):
    linkName = os.path.normpath(linkName)
    target = os.path.normpath(target)
    basePath = os.path.dirname(linkName)
    if not os.path.exists(basePath):
        os.makedirs(basePath)
    delete_link(linkName)
    if os.name == 'nt':
        cmd = [
            'cmd.exe',
            '/c',
            'mklink',
            '/j',
            linkName,
            target
        ]
        print "Executing", ' '.join(cmd)
        subprocess.check_call(cmd)
    else:
        print linkName, '-->', target
        os.symlink(os.path.abspath(target), linkName)

def copy_files(linkName, target):
    linkName = os.path.normpath(linkName)
    target = os.path.normpath(target)
    basePath = os.path.dirname(linkName)
    print(basePath)
    print(linkName)
    if not os.path.exists(basePath):
        os.makedirs(basePath)
    delete_link(linkName)
    shutil.copytree(os.path.abspath(target), linkName)

def cleanup_svn():
    cmd = [
        config.svn_binary] + config.svn_additional_params + [
        'cleanup',
        config.resources
    ]
    print "Executing", ' '.join(cmd)
    subprocess.check_call(cmd)

svn_output = []
def update_svn(path, revision):
    path = os.path.normpath(path)
    cmd = [
        config.svn_binary] + config.svn_additional_params + [
        'revert',
        '-R',
        path
    ]
    print "Executing", ' '.join(cmd)
    subprocess.check_call(cmd)
    cmd = [
        config.svn_binary] + config.svn_additional_params + [
        'up',
        '-r', revision,
        '--depth', 'infinity',
        path
    ]
    print "Executing", ' '.join(cmd)
    res = subprocess.check_output(cmd)

    lines = res.splitlines()
    for line in lines:
        print line
        if line.startswith("Updating") or "revision" in line:
            svn_output.append(line)

#load local overrides
if os.path.exists(CONFIG_FILE_NAME):
    print "Reading configuration from", os.path.abspath(CONFIG_FILE_NAME)
    g = l = {}
    execfile(CONFIG_FILE_NAME, g, l)
    for name, value in g.iteritems():
        setattr(config, name, value)
else:
    #fail only if no options were specified
    if len(sys.argv) < 2:
        print "Please create config file", os.path.abspath(CONFIG_FILE_NAME)
        print "See 'config.template' for sample config"
        sys.exit(1)

#parse options from command-line
#overrides config
if len(sys.argv) > 1:
    parser = argparse.ArgumentParser()
    for n, v in config.__dict__.iteritems():
        if n.startswith('_'):
            continue
        kwargs = {}
        if isinstance(v, bool):
            kwargs['action'] = 'store_const'
            kwargs['const'] = 'True'
        if isinstance(v, list):
            kwargs['nargs'] = '+'
        parser.add_argument('--' + n, **kwargs)
    args = parser.parse_args()
    for n, v in args.__dict__.iteritems():
        if v is not None:
            setattr(config, n, v)

def pub_version():
    cmd = [config.dart_binary, '--version']
    result = subprocess.check_call(cmd)

def pub_get(path):
    cmd = [config.dart_binary, 'pub', 'get']
    try:
        result = subprocess.check_call(cmd, cwd=path)
    except subprocess.CalledProcessError:
        return False

    return True

def unify_pubspec_lock(path):
    lines =[]
    with open(path, mode='r') as file:
         lines = file.readlines()
    with open(path, "w") as file:
        for line in lines:
            file.write(line.replace(r"\\", "/"))

def init_projects(proj_path):
    path_proj = os.path.normpath(proj_path)
    for pubspec in glob.glob(os.path.join(path_proj,'**/pubspec.yaml')):
        proj_dir = os.path.dirname(pubspec)
        print
        print proj_dir, ':'
        pubGetResult = False
        retryCount = 0

        while pubGetResult == False:
            print "Trying execute pub get. Retrycount = " + str(retryCount)
            retryCount += 1
            pubGetResult = pub_get(proj_dir)
        
        unify_pubspec_lock(os.path.join(proj_dir, "pubspec.lock"))

def generate_views_base(assets_path, debug_assets_path, games_common_assets_path, output):
    if not GEN_VIEWS_FOLDERS:
        return
    cmd = [config.dart_binary, config.view_generator_path + "/bin/main.dart", "--outputLibPath", output]
    for view_folder in GEN_VIEWS_FOLDERS:
        cmd.append('--resourcesPath')
        if view_folder == LOBBY_FOLDER_SRC:
            path_src = assets_path
        else:
            path_src = os.path.join(assets_src, view_folder)
        cmd.append(path_src)
    cmd.append('--exclude')
    cmd.append(FEATURES_FOLDER_DEST)
    subprocess.check_call(cmd)
    cmd = ["python", "export_generator.py", output]
    subprocess.check_call(cmd)
    unify_pubspec_lock(os.path.join(config.view_generator_path, "pubspec.lock"))
    cmd = [config.dart_binary, 'format', output, '--line-length', '180']
    subprocess.check_call(cmd)

def generate_bundle_views_base(assets_path, output, package_name, view_info_prefix, class_name_prefix, use_root_in_view_generator):    
    cmd = [config.dart_binary, config.view_generator_path + "/bin/main.dart", 
        "--outputLibPath", output, 
        "--resourcesPath", assets_path, 
        "--onlyClasses", 
        "--packageName", package_name,
        "--viewInfoPrefix", view_info_prefix,
        "--classNamePrefix", class_name_prefix
    ]

    if use_root_in_view_generator:
        cmd.append("--useRootIfPossible")

    print(cmd)
    
    subprocess.check_call(cmd)
    cmd = ["python", "export_generator.py", os.path.join(PROJ_FILES, package_name)]
    subprocess.check_call(cmd)
    cmd = [config.dart_binary, 'format', output, '--line-length', '180']
    subprocess.check_call(cmd)

def update_pathes_in_wads(path_dst, main_folder, secondary_folder, locale, res, is_feature_patch):
    #update pathes in resources
    for root, subdir, filenames in os.walk(path_dst, topdown=False):
        for file in filenames:
            if file.endswith(".wad.xml"):
                file_path = os.path.join(root, file)
                with open(file_path,'r') as file_read:
                    filedata = file_read.read()
                    main_assets_path = os.path.join("assets", locale, res, main_folder).replace("\\", "/").strip("/")
                    secondary_assets_path = os.path.join("assets", locale, res, secondary_folder).replace("\\", "/").strip("/")
                    filedata = filedata.replace(secondary_assets_path, main_assets_path)
                    if is_feature_patch:
                        p = pathlib.Path(main_folder)
                        main_folder_no_lobby = str(pathlib.Path(*p.parts[1:])).replace("\\", "/").strip("/")
                        filedata = filedata.replace(secondary_folder.replace("\\", "/").strip("/"), main_folder_no_lobby)
                with open(file_path,'w') as file_write:
                    file_write.write(filedata)

def remove_mangled_secondary_files_from_main_folder(resources, main_path, secondary_path):
    if "mangled" in resources:
        for root_secondary, subdir_secondary, files_secondary in os.walk(secondary_path, topdown=False, followlinks=True):
            for file in files_secondary:
                file_secondary_relpath = os.path.relpath(os.path.join(root_secondary, file), secondary_path)
                first_dot_index = file.find('.') + 1
                if first_dot_index != -1 and file.endswith(".wad.xml"):
                    mangled_file_to_remove = file[first_dot_index:][:-8]
                    for root_main, subdir_main, files_main in os.walk(main_path, topdown=False, followlinks=True):
                        for file_in_dst in files_main:
                            file_in_dst_relpath = os.path.relpath(os.path.join(root_main, file_in_dst), main_path)
                            delete_file_path = os.path.join(main_path, file_in_dst_relpath)
                            if os.path.isfile(delete_file_path) and file_in_dst.endswith("." + mangled_file_to_remove + ".wad.xml"):
                                os.remove(delete_file_path)
                                print("Delete " + delete_file_path)

#def remove_mangled_secondary_files_from_main_folder(resources, main_path, secondary_path):
#    print 'delete from main folder:', main_path, secondary_path
#    if "mangled" in resources:
#        for root, subdir, filenames in os.walk(secondary_path, topdown=False, followlinks=True):
#            for file in filenames:
#                print 'file to delete', root, ' ', subdir, ' ', file
#                first_dot_index = file.find('.') + 1
#                if first_dot_index != -1 and file.endswith(".wad.xml"):
#                    mangled_file_to_remove = file[first_dot_index:][:-8]
#                    for file_in_dst in os.listdir(main_path):
#                        delete_file_path = os.path.join(main_path, file_in_dst)
#                        if os.path.isfile(delete_file_path) and file_in_dst.endswith("." + mangled_file_to_remove + ".wad.xml"):
#                            os.remove(delete_file_path)
#                            print("Delete " + delete_file_path)

def process_secondary_lobby_folder(resources, assets_dst, assets_src, secondary_folder, revision, locale, res):
    path_dst = os.path.join(assets_dst, LOBBY_FOLDER_SRC)
    path_src_secondary = os.path.join(assets_src, secondary_folder)
    update_svn(path_src_secondary, revision)
    remove_mangled_secondary_files_from_main_folder(resources, path_dst, path_src_secondary)
    distutils.dir_util.copy_tree(os.path.abspath(path_src_secondary), path_dst)
    update_pathes_in_wads(path_dst, LOBBY_FOLDER_SRC, secondary_folder, locale, res, False)

def generate_html():
    generate_index_html()

def generate_index_html():
    params = {'is_website': "true" in config.website.lower()}
    print 'generate index.html with params:', params 
    cmd = ['python', os.path.join(config.html_template_processor, 'template_processor.py'),
    '-template_name', 'index_template.html',
    '-output_path', os.path.join(os.path.abspath(os.getcwd()), '../src/gambino/web/index.html'),
    '-params', json.dumps(params)]

    with open('template_processor.log', 'w') as f:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE)
        for line in iter(process.stdout.readline, ''):
            sys.stdout.write(line)
            f.write(line)
    os.remove('template_processor.log')

def remove_path(path_to_remove):
    if os.path.exists(path_to_remove):
        if os.path.isdir(path_to_remove):
            print "Removing " + path_to_remove
            shutil.rmtree(path_to_remove)
        else:
            print "Removing " + path_to_remove
            os.remove(path_to_remove)


if __name__ == '__main__':
    pub_version()
    if "true" in config.cleanup.lower():
        print "cleanup svn"
        cleanup_svn()
        
    assets_dst_root = os.path.join(PROJ_FILES, DST_DART_MODULE, 'web', 'assets')
    delete_link(assets_dst_root)
    for locale in config.locales:
        for res in config.resolutions:
            assets_dst = os.path.join(assets_dst_root, locale, res)
            assets_src = os.path.join(config.resources, locale, res)
            print "..............................." + assets_src

            map_json_filename = '{0}.{1}.{2}'.format(locale, res, MAP_FILE_EXT)
            map_json_dst_path = os.path.join(assets_dst_root, map_json_filename)
            #update lobby
            for lobby_folder in LOBBY_FOLDERS:
                path_src = os.path.join(assets_src, lobby_folder)
                path_dst = os.path.join(assets_dst, lobby_folder)
                
                
                update_svn(path_src, config.lobby_revision)
                if lobby_folder == LOBBY_FOLDER_SRC:
                    print("Create copy files")
                    copy_files(path_dst, path_src)
                else:
                    make_links(path_dst, path_src)
            
            if LOBBY_FOLDER_SRC_SECONDARY:
                process_secondary_lobby_folder(config.resources, assets_dst, assets_src, LOBBY_FOLDER_SRC_SECONDARY, config.lobby_revision_secondary, locale, res)
                
            if LOBBY_FOLDER_SRC_ADDITIONAL:  
                process_secondary_lobby_folder(config.resources, assets_dst, assets_src, LOBBY_FOLDER_SRC_ADDITIONAL, config.lobby_revision_additional, locale, res)
                    
            if RESOURCE_BUNDLES:
                update_svn(os.path.join(assets_src, "Features"), "HEAD")
                update_svn(os.path.join(assets_src, "Feature_skins"), "HEAD")
                for bundle in RESOURCE_BUNDLES:
                    src_path = os.path.join(assets_src, bundle.src_folder)
                    dest_path = os.path.join(assets_dst, LOBBY_FOLDER_SRC, FEATURES_FOLDER_DEST, bundle.bundle_name)
                    update_svn(src_path, bundle.svn_version)
                    if bundle.src_files:
                        raise Exception('src_files for RESOURCE_BUNDLES support is not implemented')
                        # for src_file in bundle.src_files:
                        #     base_path = os.path.dirname(dest_path)
                        #     if not os.path.exists(dest_path):
                        #         os.makedirs(dest_path)
                        #     shutil.copy(os.path.join(src_path, src_file), dest_path)                        
                    else:
                        distutils.dir_util.copy_tree(src_path, dest_path)                    
                    update_pathes_in_wads(dest_path, os.path.join(LOBBY_FOLDER_SRC, FEATURES_FOLDER_DEST, bundle.bundle_name), bundle.src_folder, locale, res, False)
                    if bundle.patch_folder and bundle.patch_version:
                        bundle_src_path = os.path.join(assets_src, bundle.patch_folder)
                        update_svn(bundle_src_path, bundle.patch_version)
                        remove_mangled_secondary_files_from_main_folder(config.resources, dest_path, bundle_src_path)
                        distutils.dir_util.copy_tree(os.path.abspath(bundle_src_path), dest_path)
                        update_pathes_in_wads(dest_path, os.path.join(LOBBY_FOLDER_SRC, FEATURES_FOLDER_DEST, bundle.bundle_name), bundle.patch_folder, locale, res, True)

            #update games
            for games_folder in GAMES_FOLDERS:
                path_src = os.path.join(assets_src, games_folder)
                path_dst = os.path.join(assets_dst, games_folder)
                update_svn(path_src, config.games_revision)
                make_links(path_dst, path_src)

            #update specific games 
            for specificGame in config.specific_games_revisions:
                for games_folder in GAMES_FOLDERS:
                    gamesDirs = os.listdir(os.path.join(assets_src, games_folder))
                    for gameDirectory in gamesDirs:
                        if(gameDirectory == specificGame[0]):
                            path_src = os.path.join(assets_src, games_folder, gameDirectory)
                            path_dst = os.path.join(assets_dst, games_folder, gameDirectory)
                            update_svn(path_src, specificGame[1])

            if TO_REMOVE:
                for res_path in TO_REMOVE:
                    path_to_remove = os.path.join(assets_dst, LOBBY_FOLDER_SRC, res_path)
                    remove_path(path_to_remove)

            #generate map.json
            if "mangled" in config.resources:
                map_json_dst = {}
                assets_abs_path = \
                    os.path.join(os.path.abspath(os.path.join(os.getcwd(), os.pardir)), "src", "gambino", "web")
                for root, subdir, filenames in os.walk(assets_dst, topdown=False, followlinks=True):
                    assets_rel_path = os.path.relpath(root, assets_abs_path)
                    for file in filenames:
                        if file.endswith(".wad.xml") or re.match(".*icon.*\.png$", file):
                            original_wad_name = '.'.join(file.split('.')[1:])
                            original_path = os.path.join(assets_rel_path, original_wad_name).replace('\\', '/')
                            mangled_path = os.path.join(assets_rel_path, file).replace('\\', '/')
                            map_json_dst[original_path] = mangled_path

                # remove mungled file names
                if TO_REMOVE:
                    for res_path in TO_REMOVE:
                        path_to_remove = os.path.join('assets', locale, res, LOBBY_FOLDER_SRC, res_path).replace('\\', '/')
                        if path_to_remove in map_json_dst:                            
                            path_to_remove = map_json_dst.pop(path_to_remove)
                            remove_path(os.path.join(PROJ_FILES, DST_DART_MODULE, 'web', path_to_remove))

                with open(map_json_dst_path, 'w') as f:
                    json.dump(map_json_dst, f)




    pub_get(config.view_generator_path)
    print "Generate base views"
    #resources_path = os.path.join(config.resources, config.locales[0], config.resolutions[0], LOBBY_FOLDER_SRC)
    resources_path = os.path.join(os.path.join(assets_dst_root, locale, res), LOBBY_FOLDER_SRC)
    print(resources_path)
    debug_resources_path = os.path.join(config.resources, config.locales[0], config.resolutions[0], DEBUG_FOLDER_SRC)
    games_common_resources_path = os.path.join(config.resources, config.locales[0], config.resolutions[0], GAMES_COMMON_FOLDER_SRC)
    out_base_views = os.path.join(PROJ_FILES, BASE_VIEW_NAMESPACE)
    generate_views_base(resources_path, debug_resources_path, games_common_resources_path, out_base_views)
    print "Resolve dependencies"

    if RESOURCE_BUNDLES:
        for bundle in RESOURCE_BUNDLES:
            resources_path = os.path.join(os.path.join(assets_dst_root, locale, res), LOBBY_FOLDER_SRC, FEATURES_FOLDER_DEST, bundle.bundle_name)
            generate_bundle_views_base(resources_path, os.path.join(PROJ_FILES, bundle.view_gen_path), bundle.namespace, bundle.bundle_name, bundle.class_name_prefix, bundle.use_root_in_view_generator)

    if "true" in config.remove_assets.lower():
        delete_link(assets_dst_root)

    if "true" in config.download_stage_list.lower():
        url = 'https://storage.googleapis.com/dev_stages/stages_list.json?v=' + str(randrange(1, 1000000))
        r = requests.get(url)

        with open('../src/gambino/web/stages_list.json', 'wb') as f:
            f.write(r.content)
            print "Downloaded stages_list.json"
    else:
        print "Downloading stages_list.json is disabled"

    init_projects(PROJ_FILES)
    
    generate_html()

    for line in svn_output:
        print ">>>> " + line
